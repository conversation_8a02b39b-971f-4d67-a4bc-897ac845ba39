name: store
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  firebase_core: ^3.12.1
  firebase_auth: ^5.5.1
  firebase_messaging: ^15.2.4
  cloud_firestore: ^5.6.5
  firebase_database: ^11.3.4
  firebase_app_check: ^0.3.2+4
  firebase_storage: ^12.4.4
  crypto: ^3.0.6

  country_code_picker: ^3.2.0

  dotted_border: ^2.1.0
  geocoding: ^3.0.0
  googleapis_auth: ^1.6.0

  cached_network_image: ^3.4.1
  flutter_easyloading: ^3.0.5
  flutter_email_sender: ^7.0.0
  flutter_html: any
  flutter_local_notifications: ^18.0.1
  flutter_svg: ^2.0.17
  geolocator: any
  location: ^8.0.0
  get: ^4.7.2
  google_sign_in: ^6.2.2
  image_picker: ^1.1.2
  intl: ^0.19.0
  pin_code_fields: ^8.0.1

  provider: ^6.1.2
  qr_flutter: ^4.1.0
  shared_preferences: ^2.5.2
  sign_in_with_apple: ^6.1.4
  syncfusion_flutter_datepicker: ^28.2.9
  timer_count_down: ^2.2.2
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  date_picker_timeline: ^1.2.6
  flutter_rating_bar: ^4.0.1
  datetime_picker_formfield_new: ^2.1.0
  flutter_polyline_points: ^2.1.0
  clipboard: ^0.1.3
  map_launcher: ^3.5.0
  story_view: ^0.16.5
  bottom_picker: ^2.11.1
  multi_select_flutter: ^4.1.3
  in_app_review: ^2.0.10
  share_plus: ^10.1.4
  photo_view: ^0.15.0
  path_provider: ^2.1.5
  video_compress: ^3.1.4
  mime: ^2.0.0
  mailer: ^6.4.1
  dropdown_search: ^5.0.6
  flutter_multi_formatter: ^2.13.0
  audioplayers: ^6.2.0

  flutter_map: ^8.1.1
  latlong2: ^0.9.1

  video_player: ^2.9.3
  google_maps_flutter: ^2.10.1
  google_maps_place_picker_mb: ^3.1.2
  http: ^1.2.2

  esc_pos_utils: any
  print_bluetooth_thermal: ^1.1.6
  syncfusion_flutter_pdf: ^28.2.9

  # Additional dependencies for imports
  rxdart: ^0.28.0

  #payment dependency
  flutter_stripe: ^11.5.0
  razorpay_flutter: ^1.4.0
  flutter_paypal: ^0.2.1
  # xendit: ^0.0.25


dependency_overrides:
  webview_flutter: ^4.8.0
  http: ^1.3.0
  google_api_headers: ^4.4.1
  package_info_plus: ^8.3.0
  syncfusion_flutter_core: ^28.2.9

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  fonts:
    - family: Urbanist-Black
      fonts:
        - asset: assets/fonts/Urbanist-Black.ttf

    - family: Urbanist-Bold
      fonts:
        - asset: assets/fonts/Urbanist-Bold.ttf

    - family: Urbanist-ExtraBold
      fonts:
        - asset: assets/fonts/Urbanist-ExtraBold.ttf

    - family: Urbanist-ExtraLight
      fonts:
        - asset: assets/fonts/Urbanist-ExtraLight.ttf

    - family: Urbanist-Light
      fonts:
        - asset: assets/fonts/Urbanist-Light.ttf

    - family: Urbanist-Medium
      fonts:
        - asset: assets/fonts/Urbanist-Medium.ttf

    - family: Urbanist-Regular
      fonts:
        - asset: assets/fonts/Urbanist-Regular.ttf

    - family: Urbanist-SemiBold
      fonts:
        - asset: assets/fonts/Urbanist-SemiBold.ttf

    - family: Urbanist-Thin
      fonts:
        - asset: assets/fonts/Urbanist-Thin.ttf

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

