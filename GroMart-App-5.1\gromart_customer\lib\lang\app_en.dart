const Map<String, String> enUS = {
  "Please wait": "Please wait",
  "Add Address": "Add Address",
  "Home": "Home",
  "Use my current location": "Use my current location",
  "Add Location": "Add Location",
  "Saved Addresses": "Saved Addresses",
  "Saved addresses not found": "Saved addresses not found",
  "Default": "Default",
  "Edit": "Edit",
  "Delete": "Delete",
  "Cancel": "Cancel",
  "Choose Current Location": "Choose Current Location",
  "Save as": "Save as",
  "Work": "Work",
  "Hotel": "Hotel",
  "House/Flat/Floor No.": "House/Flat/Floor No.",
  "Apartment/Road/Area": "Apartment/Road/Area",
  "Nearby landmark": "Nearby landmark",
  'Nearby landmark (Optional)': 'Nearby landmark (Optional)',
  "Save Address Details": "Save Address Details",
  'Please Select': 'Please Select',
  'Gallery': 'Gallery',
  'Camera': 'Camera',
  "Please select Location": "Please select Location",
  "Please Enter Flat / House / Flore / Building":
      "Please Enter Flat / House / Flore / Building",
  "Please Enter Area / Sector / locality":
      "Please Enter Area / Sector / locality",
  'Highlights for you': 'Highlights for you',
  'Highlights for you not found.': 'Highlights for you not found.',
  "Skip": "Skip",
  "Back": "Back",
  "Welcome Back! 👋": "Welcome Back! 👋",
  'Access your account to start shopping and enjoy a personalized grocery experience.':
      'Access your account to start shopping and enjoy a personalized grocery experience.',
  "Log in to continue enjoying delicious food delivered to your doorstep.":
      "Log in to continue enjoying delicious food delivered to your doorstep.",
  "Email Address": "Email Address",
  'Phone Number': 'Phone Number',
  'Enter Phone Number': 'Enter Phone Number',
  'Send OTP': 'Send OTP',
  'Please enter mobile number': 'Please enter mobile number',
  'or': 'or',
  'Hey there, thanks for choosing Foodie. Hope you love our product. If you do, share it with your friends using code':
      'Hey there, thanks for choosing Foodie. Hope you love our product. If you do, share it with your friends using code',
  'and get': 'and get',
  'Continue with Email': 'Continue with Email',
  "Enter email address": "Enter email address",
  "Password": "Password",
  "Enter password": "Enter password",
  "Forgot Password": "Forgot Password",
  "Login": "Login",
  'Search the dish, store, item, meals': 'Search the dish, store, item, meals',
  "Please enter valid email": "Please enter valid email",
  "Please enter valid password": "Please enter valid password",
  "Continue with Mobile Number": "Continue with Mobile Number",
  'Please enter Phone number': 'Please enter Phone number',
  'with Google': 'with Google',
  'with Apple': 'with Apple',
  "Didn’t have an account": "Didn’t have an account",
  "Sign up": "Sign up",
  "Verify Your Number 📱": "Verify Your Number 📱",
  "Enter the OTP sent to your mobile number.":
      "Enter the OTP sent to your mobile number.",
  "Verify & Next": "Verify & Next",
  "Verify otp": "Verify otp",
  "Invalid Code": "Invalid Code",
  "Enter Valid otp": "Enter Valid otp",
  "Did’t receive any code? ": "Did’t receive any code? ",
  "Send Again": "Send Again",
  "Create an Account 🚀": "Create an Account 🚀",
  'Sign up with your mobile number or email to start shopping on GroMart.':
      'Sign up with your mobile number or email to start shopping on GroMart.',
  "Sign up to start your food adventure with Foodie":
      "Sign up to start your food adventure with Foodie",
  "First Name": "First Name",
  "Enter First Name": "Enter First Name",
  "Last Name": "Last Name",
  'Name': 'Name',
  "Enter Last Name": "Enter Last Name",
  "Confirm Password": "Confirm Password",
  "Enter Confirm Password": "Enter Confirm Password",
  "Referral Code(Optional)": "Referral Code(Optional)",
  "Signup": "Signup",
  'Enter your mobile number to securely log in to your GroMart account.':
      'Enter your mobile number to securely log in to your GroMart account.',
  "Please enter first name": "Please enter first name",
  "Please enter last name": "Please enter last name",
  "Password and Confirm password doesn't match":
      "Password and Confirm password doesn't match",
  "Please enter minimum 6 characters password":
      "Please enter minimum 6 characters password",
  "Please enter password": "Please enter password",
  "Please enter Confirm password": "Please enter Confirm password",
  "Item Not available": "Item Not available",
  "Out of stock": "Out of stock",
  "Variants": "Variants",
  "Addons": "Addons",
  "Item Information's": "Item Information's",
  "Delivery Type": "Delivery Type",
  "Instant Delivery": "Instant Delivery",
  'instant': 'instant',
  'Add item': 'Add item',
  'schedule': 'schedule',
  'Schedule Time': 'Schedule Time',
  'Favourite Items not found.': 'Favourite Items not found.',
  "Standard": "Standard",
  'Free Delivery': 'Free Delivery',
  "Your preferred time": "Your preferred time",
  "Offers & Benefits": "Offers & Benefits",
  "Apply Coupons": "Apply Coupons",
  "Bill Details": "Bill Details",
  "Item totals": "Item totals",
  "Delivery Fee": "Delivery Fee",
  "Coupon Discount": "Coupon Discount",
  "Special Discount": "Special Discount",
  "Delivery Tips": "Delivery Tips",
  "Remove": "Remove",
  "To Pay": "To Pay",
  "Thanks with a tip!": "Thanks with a tip!",
  "Around the clock, our delivery partners bring you your favorite meals. Show your appreciation with a tip.":
      "Around the clock, our delivery partners bring you your favorite meals. Show your appreciation with a tip.",
  "Other": "Other",
  'The total price must be greater than or equal to the coupon discount value for the code to apply. Please review your cart total.':
      'The total price must be greater than or equal to the coupon discount value for the code to apply. Please review your cart total.',
  "Remarks": "Remarks",
  "Write remarks for the store": "Write remarks for the store",
  "Pay Via": "Pay Via",
  "Pay Now": "Pay Now",
  "Please select payment method": "Please select payment method",
  'Please Enter minimum amount of': 'Please Enter minimum amount of',
  "Tips Amount": "Tips Amount",
  "Enter Tips Amount": "Enter Tips Amount",
  "Add": "Add",
  '"Please enter tips Amount': '"Please enter tips Amount',
  "Coupon Code": "Coupon Code",
  "Enter coupon code": "Enter coupon code",
  'Invalid Coupon': 'Invalid Coupon',
  'Coupon code not applied': 'Coupon code not applied',
  "Apply": "Apply",
  'Off': 'Off',
  'Your item is on its way! Sit tight and we’ll handle the rest.':
      'Your item is on its way! Sit tight and we’ll handle the rest.',
  "Fix Price": "Fix Price",
  "Tap To Apply": "Tap To Apply",
  "Order Placed": "Order Placed",
  "Your delicious meal is on its way! Sit tight and we’ll handle the rest.":
      "Your delicious meal is on its way! Sit tight and we’ll handle the rest.",
  "Order ID": "Order ID",
  "Placing your order": "Placing your order",
  "Review your items and proceed to checkout for a delicious experience.":
      "Review your items and proceed to checkout for a delicious experience.",
  "Delivery Address": "Delivery Address",
  "Order Summary": "Order Summary",
  'Order': 'Order',
  "Track Order": "Track Order",
  "Payment Option": "Payment Option",
  "Preferred Payment": "Preferred Payment",
  "Other Payment Options": "Other Payment Options",
  "Change Language": "Change Language",
  "Select your preferred language for a personalized app experience.":
      "Select your preferred language for a personalized app experience.",
  "Type message here....": "Type message here....",
  "text": "text",
  "Send Media": "Send Media",
  "Choose image from gallery": "Choose image from gallery",
  "Choose video from gallery": "Choose video from gallery",
  "Take a picture": "Take a picture",
  "Record video": "Record video",
  "Driver Inbox": "Driver Inbox",
  "No Conversion found": "No Conversion found",
  "Store Inbox": "Store Inbox",
  "orderId": "orderId",
  "storeId": "storeId",
  "customerId": "customerId",
  "customerProfileImage": "customerProfileImage",
  "storetProfileImage": "storeProfileImage",
  "token": "token",
  "chatType": "chatType",
  "Double press to exit": "Double press to exit",
  "Favourites": "Favourites",
  "Orders": "Orders",
  "Profile": "Profile",
  "Wallet": "Wallet",
  "Peoples": "Peoples",
  "View in Map": "View in Map",
  "tel": "tel",
  "Call Now": "Call Now",
  "Booking Details": "Booking Details",
  "Date and Time": "Date and Time",
  "Guest": "Guest",
  "Discount": "Discount",
  "Upcoming": "Upcoming",
  "History": "History",
  "Upcoming Booking not found.": "Upcoming Booking not found.",
  "History not found.": "History not found.",
  "Guest Number": "Guest Number",
  "Book Table": "Book Table",
  "Numbers of Guests": "Numbers of Guests",
  "When are you visiting?": "When are you visiting?",
  "Today": "Today",
  "Tomorrow": "Tomorrow",
  "Select time slot and scroll to see offers":
      "Select time slot and scroll to see offers",
  "Special Occasion": "Special Occasion",
  "Clear": "Clear",
  "Is this your first visit?": "Is this your first visit?",
  "Personal Details": "Personal Details",
  "Additional Requests": "Additional Requests",
  "Add message here....": "Add message here....",
  "Book Now": "Book Now",
  "Ratings": "Ratings",
  "Open": "Open",
  "Close": "Close",
  "View Timings": "View Timings",
  "for two": "for two",
  "Also applicable on food delivery": "Also applicable on food delivery",
  "Please log in to the application. You are not logged in.":
      "Please log in to the application. You are not logged in.",
  "Table Booking": "Table Booking",
  "Quick Conformations": "Quick Conformations",
  "Available food delivery": "Available food delivery",
  "in 30-45 mins.": "in 30-45 mins.",
  "Menu": "Menu",
  "Location, Timing & Costs": "Location, Timing & Costs",
  "View on Map": "View on Map",
  '(approx)': '(approx)',
  "Timing": "Timing",
  "Cost for Two": "Cost for Two",
  'Search the dish, item, meals and more...':
      'Search the dish, item, meals and more...',
  "Cuisines": "Cuisines",
  "Book a table at your favorite store and enjoy a delightful dining experience.":
      "Book a table at your favorite store and enjoy a delightful dining experience.",
  "No store Found in Your Area": "No store Found in Your Area",
  "Currently, there are no available store in your zone. Try changing your location to find nearby options.":
      "Currently, there are no available store in your zone. Try changing your location to find nearby options.",
  'Change Zone': 'Change Zone',
  'Explore the Categories': 'Explore the Categories',
  "New Arrivals": "New Arrivals",
  "View all": "View all",
  "Popular Stores": "Popular Stores",
  "All Stores": "All Stores",
  "Sorry, The Zone is not available in your area. change the other location first.":
      "Sorry, The Zone is not available in your area. change the other location first.",
  "Could not launch": "Could not launch",
  "Categories": "Categories",
  'Preferences': 'Preferences',
  'Set Your Delivery Location': 'Set Your Delivery Location',
  "Profile Information": "Profile Information",
  "View and update your personal details, contact information, and preferences.":
      "View and update your personal details, contact information, and preferences.",
  "Save Details": "Save Details",
  "please select": "please select",
  'Enter your address or use your current location to find the best grocery options available in your area.':
      'Enter your address or use your current location to find the best grocery options available in your area.',
  "camera": "camera",
  "gallery": "gallery",
  "Your Favourites, All in One Place": "Your Favourites, All in One Place",
  "Please Log In to Continue": "Please Log In to Continue",
  "You’re not logged in. Please sign in to access your account and explore all features.":
      "You’re not logged in. Please sign in to access your account and explore all features.",
  "Log in": "Log in",
  'items': 'items',
  'View Cart': 'View Cart',
  'Favourite Items': 'Favourite Items',
  'Favourite Store not found.': 'Favourite Store not found.',
  "Favourite Stores": "Favourite Stores",
  'Favourite Foods not found.': 'Favourite Foods not found.',
  "Favourite Foods": "Favourite Foods",
  "Favourite Stores not found.": "Favourite Stores not found.",
  "Favourite Foods not found": "Favourite Foods not found",
  "Non Veg.": "Non Veg.",
  "Pure veg.": "Pure veg.",
  'Foods': 'Foods',
  'Veg': 'Veg',
  'Non Veg': 'Non Veg',
  "No worries!! We’ll send you reset instructions":
      "No worries!! We’ll send you reset instructions",
  "Customize Gift Card": "Customize Gift Card",
  "Choose an amount": "Choose an amount",
  "Enter gift card amoun": "Enter gift card amoun",
  "Add Message (Optional)": "Add Message (Optional)",
  "Continue": "Continue",
  "Please enter Amount": "Please enter Amount",
  'Enter gift card amount': 'Enter gift card amount',
  'Amount': 'Amount',
  'Enter Amount': 'Enter Amount',
  "Complete payment and share this e-gift card with loved ones using any app":
      "Complete payment and share this e-gift card with loved ones using any app",
  "Sub Total": "Sub Total",
  'Pay': 'Pay',
  'km': 'km',
  "Grand Total": "Grand Total",
  'days after purchase': 'days after purchase',
  'Gift Card expire': 'Gift Card expire',
  "Gift Card expire days after purchase":
      "Gift Card expire days after purchase",
  "Purchased Gift card not found": "Purchased Gift card not found",
  "Gift Code": "Gift Code",
  "Gift Pin": "Gift Pin",
  "Redeemed": "Redeemed",
  'Redeem': 'Redeem',
  "Not Redeem": "Not Redeem",
  "Redeem Gift Card": "Redeem Gift Card",
  "Enter your gift card code to enjoy discounts and special offers on your orders.":
      "Enter your gift card code to enjoy discounts and special offers on your orders.",
  "Enter gift code": "Enter gift code",
  "Enter gift pin": "Enter gift pin",
  "Please Enter Gift Code": "Please Enter Gift Code",
  "Please Enter Gift Pin": "Please Enter Gift Pin",
  "Gift voucher already redeemed": "Gift voucher already redeemed",
  "Gift Pin Invalid": "Gift Pin Invalid",
  "Gift Voucher expire": "Gift Voucher expire",
  "success": "success",
  "Voucher redeem successfully": "Voucher redeem successfully",
  'Payment successfully': 'Payment successfully',
  "Invalid Gift Code": "Invalid Gift Code",
  "Something went wrong, please contact admin.":
      "Something went wrong, please contact admin.",
  "No Store found": "No Store found",
  '% OFF': '% OFF',
  ' OFF': ' OFF',
  "Search the dish, store, food, meals": "Search the dish, store, food, meals",
  "Our Categories": "Our Categories",
  "See all": "See all",
  'Best Servings item': 'Best Servings item',
  "Best Servings Food": "Best Servings Food",
  "Large Discounts": "Large Discounts",
  "Save Upto 50% Off": "Save Upto 50% Off",
  '% off': '% off',
  'off': 'off',
  'Payment Failed': 'Payment Failed',
  "Stories": "Stories",
  'Best item Stories Ever': 'Best item Stories Ever',
  'sent a message': 'sent a message',
  'Sent a video': 'Sent a video',
  'Sent a audio': 'Sent a audio',
  "Best Food Stories Ever": "Best Food Stories Ever",
  'Stores': 'Stores',
  'Payment UnSuccessful!!': 'Payment UnSuccessful!!',
  'Payment Successful!!': 'Payment Successful!!',
  "Best Stores": "Best Stores",
  "Do you really want to change the delivery option? Your cart will be empty.":
      "Do you really want to change the delivery option? Your cart will be empty.",
  "Alert": "Alert",
  'Payment Processing!! via': 'Payment Processing!! via',
  "Upto": "Upto",
  "Percentage": "Percentage",
  'Payment Failed!!': 'Payment Failed!!',
  "Error": "Error",
  "Enable Location Services 📍": "Enable Location Services 📍",
  "To provide the best dining experience, allow Foodie to access your location.":
      "To provide the best dining experience, allow Foodie to access your location.",
  "Use Current Location": "Use Current Location",
  "Set From Map": "Set From Map",
  "Enter Manually location": "Enter Manually location",
  "Order Details": "Order Details",
  "Order Delivered.": "Order Delivered.",
  "Your Order has been Preparing and assign to the driver":
      "Your Order has been Preparing and assign to the driver",
  "Your Order": "Your Order",
  'Preparation Time': 'Preparation Time',
  "Rate us": "Rate us",
  "fix": "fix",
  'Your Order has been Preparing and assign to the driver\n Preparation Time':
      'Your Order has been Preparing and assign to the driver\n Preparation Time',
  "TakeAway": "TakeAway",
  "Schedule": "Schedule",
  "Reorder": "Reorder",
  'Item Added In a cart': 'Item Added In a cart',
  "My Order": "My Order",
  "Keep track your delivered, In Progress and Rejected food all in just one place.":
      "Keep track your delivered, In Progress and Rejected food all in just one place.",
  "All": "All",
  'Keep track your delivered, In Progress and Rejected item all in just one place.':
      'Keep track your delivered, In Progress and Rejected item all in just one place.',
  "In Progress": "In Progress",
  "Delivered": "Delivered",
  "Rejected": "Rejected",
  'Cancelled': 'Cancelled',
  "Order Not Found": "Order Not Found",
  "View Details": "View Details",
  "My Profile": "My Profile",
  "Manage your personal information, preferences, and settings all in one place.":
      "Manage your personal information, preferences, and settings all in one place.",
  "General Information": "General Information",
  "Bookings Information": "Bookings Information",
  "Social": "Social",
  "Refer a Friend": "Refer a Friend",
  "Rate the app": "Rate the app",
  "Communication": "Communication",
  "Are you sure you want to log out? You will need to enter your credentials to log back in.":
      "Are you sure you want to log out? You will need to enter your credentials to log back in.",
  "Delete Account": "Delete Account",
  "Are you sure you want to delete your account? This action is irreversible and will permanently remove all your data.":
      "Are you sure you want to delete your account? This action is irreversible and will permanently remove all your data.",
  "Account deleted successfully": "Account deleted successfully",
  "Contact Administrator": "Contact Administrator",
  "Dark Mode": "Dark Mode",
  "Rate the food": "Rate the food",
  "Rate for": "Rate for",
  "Choose a image and upload here": "Choose a image and upload here",
  "JPEG, PNG": "JPEG, PNG",
  'Rate the item': 'Rate the item',
  "Brows Image": "Brows Image",
  "Type comment": "Type comment",
  "Submit Review": "Submit Review",
  "Refer your friend and earn": "Refer your friend and earn",
  "Each🎉": "Each🎉",
  'Ok': 'Ok',
  'Copied': 'Copied',
  "Invite your friends to sign up with GroMart using your code, and you’ll earn":
      "Invite your friends to sign up with GroMart using your code, and you’ll earn",
  "Invite Friends & Businesses": "Invite Friends & Businesses",
  "Invite your friends to sign up with Foodie using your code, and you’ll earn after their Success the first order! 💸🍔":
      "Invite your friends to sign up with Foodie using your code, and you’ll earn after their Success the first order! 💸🍔",
  "after their Success the first order! 💸":
      "after their Success the first order! 💸",
  'after their Success the first order! 💸🍔':
      'after their Success the first order! 💸🍔',
  "Share Code": "Share Code",
  "Hey there, thanks for choosing GroMart. Hope you love our product. If you do, share it with your friends using code":
      "Hey there, thanks for choosing GroMart. Hope you love our product. If you do, share it with your friends using code",
  'Invite your friends to sign up with Foodie using your code, and you’ll earn':
      'Invite your friends to sign up with Foodie using your code, and you’ll earn',
  'Share': 'Share',
  "Timing is not added by store": "Timing is not added by store",
  "Also applicable on table booking": "Also applicable on table booking",
  "Additional Offers": "Additional Offers",
  "Search the dish, food, meals and more...":
      "Search the dish, food, meals and more...",
  'when order completed': 'when order completed',
  "Info": "Info",
  "Food Information's": "Food Information's",
  "Gram": "Gram",
  "Calories": "Calories",
  "Proteins": "Proteins",
  "Fats": "Fats",
  "Specification": "Specification",
  "Required • Select any 1 option": "Required • Select any 1 option",
  "Reviews": "Reviews",
  'reviews': 'reviews',
  "This vendor has reached their maximum order capacity. Please select a different vendor or try again later.":
      "This vendor has reached their maximum order capacity. Please select a different vendor or try again later.",
  "You don't have sufficient wallet balance to place order":
      "You don't have sufficient wallet balance to place order",
  'Search item & Store': 'Search item & Store',
  "Scan QRcode": "Scan QRcode",
  "Store is not available": "Store is not available",
  "Search Food & Rtore": "Search Food & Store",
  "privacy": "privacy",
  "Privacy Policy": "Privacy Policy",
  "Terms & Conditions": "Terms & Conditions",
  'Top up': 'Top up',
  "Top up Wallet": "Top up Wallet",
  "Select Top up Options": "Select Top up Options",
  "Top-up": "Top-up",
  "Keep track of your balance, transactions, and payment methods all in one place.":
      "Keep track of your balance, transactions, and payment methods all in one place.",
  "Transaction not found": "Transaction not found",
  "Foodie": "Foodie",
  'Shop from multiple vendors and enjoy a seamless grocery shopping experience right from your phone.':
      'Shop from multiple vendors and enjoy a seamless grocery shopping experience right from your phone.',
  'Welcome to GroMart': 'Welcome to GroMart',
  'You have to allow location permission to use your location':
      'You have to allow location permission to use your location',
  "Get Started": "Get Started",
  "Welcome to Foodie": "Welcome to Foodie",
  "Your Favorite Food Delivered Fast!": "Your Favorite Food Delivered Fast!",
  "Cancel Payment": "Cancel Payment",
  "Exit": "Exit",
  'Please enter tips Amount': 'Please enter tips Amount',
  "Continue Payment": "Continue Payment",
  "we Enter In": "we Enter In",
  "Foodie Customer": "Foodie Customer",
  "Gift Card": "Gift Card",
  "Share app": "Share app",
  'Check out Foodie, your ultimate food delivery application!':
      'Check out Foodie, your ultimate food delivery application!',
  "Legal": "Legal",
  'GroMart': 'GroMart',
  'App Store:': 'App Store:',
  'Check out GroMart, your ultimate item delivery application! \n\nGoogle Play:':
      'Check out GroMart, your ultimate item delivery application! \n\nGoogle Play:',
  'Google Play:': 'Google Play:',
  'Look what I made!': 'Look what I made!',
  "Delivery": "Delivery",
  "Email": "Email",
  "Didn’t have an account?": "Didn’t have an account?",
  "Log out": "Log out",
  "My Wallet": "My Wallet",
  "Payment Method": "Payment Method",
  "Largest Discounts": "Largest Discounts",
  "You denied location permission forever. Please allow location permission from your app settings and receive more accurate delivery.":
      "You denied location permission forever. Please allow location permission from your app settings and receive more accurate delivery.",
  "Terms and Conditions": "Terms and Conditions",
  "You don't have sufficient wallet balance to purchase gift card":
      "You don't have sufficient wallet balance to purchase gift card",
  "The total price must be greater than or equal to the coupon discount value for the code to apply. Please review your cart total":
      "The total price must be greater than or equal to the coupon discount value for the code to apply. Please review your cart total",
  "The total price must be greater than or equal to the special discount value for the code to apply. Please review your cart total.":
      "The total price must be greater than or equal to the special discount value for the code to apply. Please review your cart total.",
  "Gift card purchase amount debited": "Gift card purchase amount debited",
  "Gift card Purchases successfully": "Gift card Purchases successfully",
  "This user is disable please contact to administrator":
      "This user is disable please contact to administrator",
  "cancelPayment?": "Are you sure want to cancel payment?",
  'Reset Password link sent your': 'Reset Password link sent your',
  'email': 'email',
  'No user found for that email.': 'No user found for that email.',
  'Gift Code :': 'Gift Code :',
  'Gift Pin :': 'Gift Pin :',
  'Price :': 'Price :',
  'Expire Date :': 'Expire Date :',
  'Message': 'Message',
  "Please add rate for item.": "Please add rate for item.",
  'OTP sent': 'OTP sent',
  'invalid_phone_number': 'invalid phone number',
  'multiple_time_request': 'multiple time request',
  'Please add rate for food item.': 'Please add rate for food item.',
  'Referral code is Invalid': 'Refrral code is Invalid',
  'Account create successfully': 'Account create successfully',
  'The password provided is too weak.': 'The password provided is too weak.',
  'Enter email is Invalid': 'Enter email is Invalid',
  'The account already exists for that email.':
      'The account already exists for that email.',
  'Amount Top-up successfully': 'Amount Top-up successfully',
  'Something want wrong please contact administrator':
      'Something want wrong please contact administrator',
  'Search Places': 'Search Places',
  'Search your location here': 'Search your location here',
  'settings': 'settings',
  'close': 'close',
  'No user found': 'No user found',
  'This user is not created in customer application.':
      'This user is not created in customer application.',
  'Confirm Location': 'Confirm Location',
  'Picked Location:': 'Picked Location:',
  'No Location Picked': 'No Location Picked',
  'Search location...': 'Search location...',
  'PickUp Location': 'PickUp Location'
};
