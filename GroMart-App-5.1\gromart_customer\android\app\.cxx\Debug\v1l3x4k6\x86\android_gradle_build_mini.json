{"buildFiles": ["/Users/<USER>/Documents/flutter_sdk/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Project/github/GroMart-App/gromart_customer/android/app/.cxx/Debug/v1l3x4k6/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Project/github/GroMart-App/gromart_customer/android/app/.cxx/Debug/v1l3x4k6/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}