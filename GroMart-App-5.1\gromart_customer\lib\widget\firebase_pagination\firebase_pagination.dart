/// A package to paginate your firebase related data with realtime updates.
///
/// It can be used for `Firestore` and `Firebase Realtime Database`.
///
/// Data can be shown in `list`, `grid` and `scrollable wrap` view.
library firebase_pagination;

// Widgets
export 'src/realtime_db_pagination.dart';
export 'src/firestore_pagination.dart';

// Data Models
export 'src/models/view_type.dart';
export 'src/models/wrap_options.dart';
