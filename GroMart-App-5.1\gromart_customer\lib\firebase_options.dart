// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// 
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBJ9rn_oImhFkNctUQA2JKZpV_czXJ3UcI',
    appId: '1:299746043583:android:a4066d4605e7cb76fa880a',
    messagingSenderId: '299746043583',
    projectId: 'medsy-customer',
    storageBucket: 'medsy-customer.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDyAQMYv8Pp9xs2ZH4tcpNbgnB7CCWDQpw',
    appId: '1:299746043583:ios:b35bb9a8aa702c11fa880a',
    messagingSenderId: '299746043583',
    projectId: 'medsy-customer',
    storageBucket: 'medsy-customer.firebasestorage.app',
    iosBundleId: 'com.siddhi.gromart.customer',
  );
}

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBJ9rn_oImhFkNctUQA2JKZpV_czXJ3UcI',
    appId: '1:299746043583:android:a4066d4605e7cb76fa880a',
    messagingSenderId: '299746043583',
    projectId: 'medsy-customer',
    storageBucket: 'medsy-customer.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDyAQMYv8Pp9xs2ZH4tcpNbgnB7CCWDQpw',
    appId: '1:299746043583:ios:b35bb9a8aa702c11fa880a',
    messagingSenderId: '299746043583',
    projectId: 'medsy-customer',
    storageBucket: 'medsy-customer.firebasestorage.app',
    iosBundleId: 'com.siddhi.gromart.customer',
  );
}