allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
    configurations.configureEach {
        resolutionStrategy {
            force 'pl.droidsonroids.gif:android-gif-drawable:1.2.25'
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
